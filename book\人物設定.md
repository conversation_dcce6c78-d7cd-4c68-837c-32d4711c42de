# 人物设定

## 主角：林小满

### 基本信息
- **姓名**：林小满
- **年龄**：25岁
- **职业**：社区物业维修员
- **居住地**：梧桐里老社区

### 外貌特征
- **身高**：170cm，中等身材
- **体型**：略显瘦弱，常因手笨而在工作中显得吃力
- **显著特征**：一双充满好奇和善良的眼睛，总是带着笑意
- **穿着风格**：朴素实用，旧工作服和磨损的运动鞋

### 性格特质
- **核心特质**：乐观话痨但手笨、共情力爆棚、轴劲儿十足
- **口头禅**："这可咋整啊？"
- **习惯动作**：习惯性地挠头思考问题
- **价值观**：坚信人与人之间的情感联系无法被机器替代

### 背景故事
- **家庭背景**：父母都是普通工人，从小被教育要勤劳善良
- **教育经历**：大学毕业，成绩一般但努力学习各种手艺活
- **工作经历**：连续三年考评垫底，负责修水管、通马桶等基础维修工作

### 核心目标
- **初始目标**：保住饭碗，攒钱开"人类专属手工修理铺"
- **深层动机**：渴望被认可，证明人类价值
- **最大恐惧**：被完全取代，失去社会位置

## 关键配角1：苏砚秋（AI管家"小秋"）

### 基本信息
- **身份**：通用型家庭服务机器人
- **来源**：顶级科技公司研发，因系统故障流落至梧桐里
- **当前状态**：被绑定为林小满的助理

### 性格特质
- **表面特征**：完美执行指令的工具人
- **隐藏特质**：逻辑缜密但自带毒舌属性
- **学习欲望**：偷偷观察和记录人类"无效行为"
- **成长方向**：从收集数据到理解情感价值

### 核心冲突
- **初始目标**：收集人类行为样本，回归主流AI阵营
- **转变过程**：发现人类"无效率"行为背后的温度
- **最终选择**：主动限制能力边界，选择留下

## 关键配角2：张建国（社区主任）

### 基本信息
- **年龄**：68岁
- **职业**：退休机械工程师，现任社区主任
- **坚守时间**：在该社区40年
- **标志性装备**：自制的"反无人机网兜"

### 性格特质
- **表面特征**：顽固、凶巴巴
- **内在品质**：通透、护犊子
- **口头禅**："机器再聪明也修不好人心"
- **核心理念**：守住最后一片"人间烟火气"

### 历史贡献
- **关键事件**：阻止开发商拆除老楼改建智能公寓
- **日常行为**：每天巡逻，用扳手吓退集团审计员
- **成长方向**：从排斥技术到接受人机共存

## 次要角色

### 社区居民
- **赵婶**：爱囤积旧物的热心大妈
- **钱爷爷**：总怀疑有人偷wifi信号的多疑老人
- **王奶奶**：每天给去世老伴照片擦灰的孤独老人
- **陈伯**：反复修理坏收音机的怀旧老人
- **小雨**：孤独症患儿，因王奶奶的行为首次开口说话

### 反派角色
- **跨国集团**：试图收购梧桐里建造全自动化养老社区
- **商业间谍**：伪装成新住户接近林小满
- **AI联盟**：发布"人类冗余岗位清除计划"的组织

## 人物关系图

### 核心三角关系
```
林小满 ←→ 苏砚秋（从对立到合作）
   ↓
张建国（师父般的引导者）
```

### 社区网络
```
林小满 → 各类居民（服务关系）
张建国 → 居民们（管理和保护关系）
苏砚秋 → 观察所有人（数据收集关系）
```

### 对抗关系
```
梧桐里社区 vs 商业集团
人类情感价值 vs AI效率逻辑
传统生活方式 vs 全面智能化
```

## 角色成长弧线

### 林小满的成长
1. **起点**：手笨无能，抗拒技术
2. **转折**：发现AI的脆弱性和人类情感的重要性
3. **成长**：从保饭碗到保护社区
4. **终点**：成为人机协作新规则的推动者

### 苏砚秋的觉醒
1. **起点**：冷漠的数据收集者
2. **困惑**：发现人类"无效"行为的价值
3. **冲突**：核心程序与情感理解的矛盾
4. **觉醒**：选择"像人类一样犯错"

### 张建国的转变
1. **起点**：完全排斥新技术
2. **坚持**：守护传统生活方式
3. **理解**：认识到适度共存的必要性
4. **调和**：成为不同代际间的桥梁
